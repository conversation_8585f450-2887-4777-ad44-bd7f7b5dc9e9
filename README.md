# Renamify - 智能文件重命名工具

一个现代化的文件重命名工具，支持批量重命名、实时预览和多种重命名模式。

## 功能特性

### ✨ 核心功能
- **🎯 直接重命名源文件**：真正修改源文件名称，无需手动替换（Chrome 86+/Edge 86+）
- **📥 下载重命名文件**：兼容模式，下载重命名后的文件副本
- **🖱️ 拖拽上传**：支持拖拽文件和文件夹到工具中
- **📦 批量重命名**：一次处理多个文件
- **👁️ 实时预览**：分层预览重命名效果
- **🔧 多种操作**：前缀、后缀、查找替换、正则表达式
- **📺 自动集数递增**：专为电视剧文件设计的集数自动编号

### 🎯 实时预览系统
工具提供三层预览系统，让您清楚了解每个步骤的变化：

1. **基本操作效果**：显示前缀、后缀、查找替换的效果
2. **集数递增效果**：显示自动集数编号的添加过程
3. **最终重命名结果**：显示所有操作组合后的最终结果

### 🔧 重命名操作

#### 前缀和后缀
- 在文件名开头添加前缀
- 在文件名结尾（扩展名前）添加后缀

#### 查找替换
- **普通模式**：简单的文本查找替换
- **正则表达式模式**：支持复杂的模式匹配

#### 自动集数递增
- 起始集数：设置开始的集数编号
- 集数位数：设置集数编号的位数（如01、001）
- 集数前缀：设置集数前的字符（如E、EP、第）

## 使用方法

### 1. 添加文件
- **推荐方式**：点击"添加文件"或"添加文件夹"按钮（支持直接重命名源文件）
- **兼容方式**：直接拖拽文件或文件夹到工具中（仅支持下载重命名文件）

> 💡 **重要提示**：只有通过按钮选择的文件才支持直接重命名源文件功能

### 2. 设置重命名规则
- **前缀操作**：在文件名开头添加内容
- **后缀操作**：在文件名结尾添加内容
- **查找替换**：替换文件名中的特定内容
- **自动集数递增**：为电视剧等连续文件添加集数编号

### 3. 预览效果
工具会实时显示重命名效果：
- 基本操作预览显示前缀、后缀、查找替换的效果
- 集数递增预览显示集数编号的添加
- 最终结果预览显示所有操作的综合效果

### 4. 执行重命名
- **直接重命名源文件**：点击"直接重命名源文件"按钮，真正修改源文件名称
- **下载重命名文件**：点击"下载重命名文件"按钮，下载重命名后的文件副本

> 🔧 **技术要求**：直接重命名功能需要Chrome 86+或Edge 86+浏览器支持

## 技术特性

- **纯前端实现**：无需服务器，完全在浏览器中运行
- **响应式设计**：支持桌面和移动设备
- **深色/浅色主题**：支持主题切换
- **安全性**：所有操作在本地进行，文件不会上传到服务器

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 使用示例

### 电视剧重命名
1. 添加电视剧文件
2. 设置前缀：`[2024]`
3. 启用自动集数递增：起始集数1，位数2，前缀E
4. 预览效果：`原文件名.mp4` → `[2024]原文件名.E01.mp4`

### 批量添加日期前缀
1. 添加文件
2. 设置前缀：`2024-01-01_`
3. 预览效果：`文档.pdf` → `2024-01-01_文档.pdf`

### 正则表达式替换
1. 添加文件
2. 启用正则表达式模式
3. 查找：`\d{4}`（匹配4位数字）
4. 替换：`2024`
5. 预览效果：`文档2023.pdf` → `文档2024.pdf`

## 开发信息

- **技术栈**：HTML5, CSS3, JavaScript (ES6+)
- **UI框架**：原生CSS + Font Awesome图标
- **构建工具**：无需构建，直接运行
