<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File System Access API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .btn {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>File System Access API 测试</h1>
    
    <div>
        <h2>浏览器支持检测</h2>
        <button class="btn" onclick="checkSupport()">检查浏览器支持</button>
        <div id="supportResult" class="result"></div>
    </div>

    <div>
        <h2>文件选择测试</h2>
        <button class="btn" onclick="selectFile()">选择文件</button>
        <button class="btn" onclick="selectDirectory()">选择目录</button>
        <div id="fileResult" class="result"></div>
    </div>

    <div>
        <h2>文件重命名测试</h2>
        <button class="btn" onclick="testRename()">测试重命名</button>
        <div id="renameResult" class="result"></div>
    </div>

    <script>
        let selectedFileHandle = null;
        let selectedDirectoryHandle = null;

        function checkSupport() {
            const result = document.getElementById('supportResult');
            const hasFileSystemAccess = 'showOpenFilePicker' in window && 'showDirectoryPicker' in window;
            
            if (hasFileSystemAccess) {
                result.className = 'result success';
                result.textContent = '✅ 您的浏览器支持 File System Access API！\n可以使用直接文件重命名功能。';
            } else {
                result.className = 'result error';
                result.textContent = '❌ 您的浏览器不支持 File System Access API。\n请使用 Chrome 86+ 或 Edge 86+。';
            }
        }

        async function selectFile() {
            const result = document.getElementById('fileResult');
            try {
                const [fileHandle] = await window.showOpenFilePicker();
                selectedFileHandle = fileHandle;
                const file = await fileHandle.getFile();
                
                result.className = 'result success';
                result.textContent = `✅ 文件选择成功！
文件名: ${file.name}
大小: ${file.size} 字节
类型: ${file.type}
最后修改: ${new Date(file.lastModified).toLocaleString()}`;
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ 文件选择失败: ${error.message}`;
            }
        }

        async function selectDirectory() {
            const result = document.getElementById('fileResult');
            try {
                const directoryHandle = await window.showDirectoryPicker();
                selectedDirectoryHandle = directoryHandle;
                
                result.className = 'result success';
                result.textContent = `✅ 目录选择成功！
目录名: ${directoryHandle.name}`;
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ 目录选择失败: ${error.message}`;
            }
        }

        async function testRename() {
            const result = document.getElementById('renameResult');
            
            if (!selectedFileHandle || !selectedDirectoryHandle) {
                result.className = 'result error';
                result.textContent = '❌ 请先选择文件和目录';
                return;
            }

            try {
                const file = await selectedFileHandle.getFile();
                const originalName = file.name;
                const newName = 'renamed_' + originalName;

                result.textContent = `开始重命名测试...
原文件名: ${originalName}
新文件名: ${newName}

步骤1: 检查文件是否存在于目录中...`;

                // 检查文件是否在选择的目录中
                try {
                    await selectedDirectoryHandle.getFileHandle(originalName);
                    result.textContent += '\n✅ 文件存在于目录中';
                } catch (error) {
                    result.className = 'result error';
                    result.textContent += '\n❌ 文件不在选择的目录中';
                    return;
                }

                result.textContent += '\n\n步骤2: 创建新文件...';
                const newFileHandle = await selectedDirectoryHandle.getFileHandle(newName, { create: true });
                
                result.textContent += '\n✅ 新文件创建成功';
                result.textContent += '\n\n步骤3: 复制文件内容...';
                
                const writable = await newFileHandle.createWritable();
                await writable.write(file);
                await writable.close();
                
                result.textContent += '\n✅ 文件内容复制成功';
                result.textContent += '\n\n步骤4: 删除原文件...';
                
                await selectedDirectoryHandle.removeEntry(originalName);
                
                result.className = 'result success';
                result.textContent += '\n✅ 原文件删除成功\n\n🎉 文件重命名测试完成！';
                
            } catch (error) {
                result.className = 'result error';
                result.textContent += `\n\n❌ 重命名失败: ${error.message}`;
                console.error('重命名测试失败:', error);
            }
        }

        // 页面加载时自动检查支持
        window.onload = checkSupport;
    </script>
</body>
</html>
