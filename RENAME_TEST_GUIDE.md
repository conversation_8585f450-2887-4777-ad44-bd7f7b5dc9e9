# 重命名功能测试指南

## 🎯 测试目标
验证修复后的重命名功能能够正常工作，特别是连续重命名操作。

## 🔧 测试环境
- **浏览器**: Chrome 86+ 或 Edge 86+
- **测试文件**: 准备几个测试文件（如 test1.txt, test2.jpg 等）

## 📋 详细测试步骤

### 测试1：单个文件重命名
1. **添加文件**：
   - 点击"添加文件"按钮
   - 选择一个测试文件（如 `test.txt`）
   - 选择包含该文件的目录
   - 确认文件显示"✓ 可直接重命名"

2. **设置重命名规则**：
   - 在"前缀操作"中输入：`[2024]`
   - 确认预览显示：`test.txt` → `[2024]test.txt`

3. **执行重命名**：
   - 点击"直接重命名源文件"按钮
   - 观察控制台日志（按F12查看）
   - 等待成功提示

4. **验证结果**：
   - 打开文件管理器，确认文件名已变为 `[2024]test.txt`
   - 在应用中确认文件列表显示新名称

### 测试2：连续重命名（关键测试）
1. **第一次重命名**：
   - 继续使用上面重命名后的文件 `[2024]test.txt`
   - 在"后缀操作"中输入：`_backup`
   - 确认预览显示：`[2024]test.txt` → `[2024]test_backup.txt`

2. **执行第二次重命名**：
   - 点击"直接重命名源文件"按钮
   - **关键检查**：不应该出现"文件句柄不存在"错误
   - 等待成功提示

3. **验证结果**：
   - 确认文件名变为 `[2024]test_backup.txt`
   - 确认没有错误提示

### 测试3：批量文件重命名
1. **添加多个文件**：
   - 点击"添加文件"按钮
   - 选择多个文件
   - 选择包含文件的目录

2. **设置集数递增**：
   - 勾选"启用自动集数递增"
   - 设置起始集数：1
   - 设置集数位数：2
   - 设置集数前缀：E

3. **执行批量重命名**：
   - 点击"直接重命名源文件"按钮
   - 观察所有文件是否成功重命名

## 🔍 调试信息检查

### 控制台日志关键信息
打开浏览器开发者工具（F12），在Console标签中查看：

1. **文件添加时**：
   ```
   已添加 X 个文件（支持直接重命名）
   ```

2. **重命名开始时**：
   ```
   开始直接重命名操作
   当前文件列表: [...]
   需要重命名的文件数量: X
   ```

3. **每个文件重命名时**：
   ```
   开始重命名文件: 原名 → 新名
   文件对象详情: {...}
   使用创建新文件方法重命名
   获取文件内容成功，大小: XXX
   获取父目录句柄成功
   创建新文件: 新名
   文件内容写入完成
   删除旧文件: 原名
   旧文件删除成功
   文件句柄已更新，ID: XXX
   文件重命名完成: 原名 → 新名
   ```

4. **重命名完成时**：
   ```
   成功重命名 X 个文件
   ```

### 错误排查

**如果出现"文件句柄不存在"错误**：
1. 检查控制台中的"文件句柄映射"日志
2. 确认文件ID是否在映射中存在
3. 检查是否正确更新了文件句柄

**如果出现"文件不在目录中"错误**：
1. 确认选择的目录包含要重命名的文件
2. 检查文件名是否正确（特别是已重命名的文件）

## ✅ 成功标志

### 单次重命名成功
- ✅ 文件名在文件系统中确实被修改
- ✅ 应用中显示新的文件名
- ✅ 没有错误提示
- ✅ 控制台显示完整的重命名流程日志

### 连续重命名成功
- ✅ 第二次重命名不出现"文件句柄不存在"错误
- ✅ 能够基于第一次重命名的结果继续重命名
- ✅ 文件句柄正确更新和维护
- ✅ 预览显示正确的当前文件名

### 批量重命名成功
- ✅ 所有文件都成功重命名
- ✅ 集数递增正确应用
- ✅ 没有文件重命名失败
- ✅ 文件句柄映射正确维护

## 🐛 常见问题解决

### 问题1：第二次重命名失败
**症状**：第一次重命名成功，第二次提示"文件句柄不存在"
**原因**：文件句柄没有正确更新
**解决**：检查`renameFileDirectly`方法中的句柄更新逻辑

### 问题2：文件名显示错误
**症状**：重命名后文件列表显示错误的文件名
**原因**：`originalName`和`name`字段更新逻辑问题
**解决**：确保重命名后正确更新文件对象属性

### 问题3：目录验证失败
**症状**：提示"目录不包含文件"
**原因**：使用了错误的文件名进行验证
**解决**：使用当前文件名而不是原始文件名

## 🎉 测试完成确认

如果所有测试都通过，说明重命名功能已经完全修复：

1. ✅ **单个文件重命名** - 基本功能正常
2. ✅ **连续重命名** - 文件句柄正确维护
3. ✅ **批量重命名** - 多文件处理正常
4. ✅ **错误处理** - 异常情况处理完善
5. ✅ **用户体验** - 操作流畅，提示清晰

现在您可以放心使用直接重命名功能了！🚀
