# 直接重命名功能测试指南

## 🎯 测试目标
验证工具能够真正修改源文件名称，而不是仅仅下载重命名后的副本。

## 🔧 前置条件

### 浏览器要求
- ✅ **Chrome 86+** (推荐)
- ✅ **Edge 86+** 
- ❌ Firefox (不支持)
- ❌ Safari (不支持)

### 测试环境准备
1. 创建一个测试文件夹，例如：`/Users/<USER>/Desktop/rename_test/`
2. 在文件夹中放入几个测试文件，例如：
   - `test1.txt`
   - `test2.jpg` 
   - `episode1.mp4`
   - `episode2.mp4`

## 📋 详细测试步骤

### 步骤1：打开应用
1. 在Chrome或Edge中打开 `index.html`
2. 确认看到绿色通知："✅ 您的浏览器支持直接文件重命名功能！"
3. 确认页面顶部有"直接重命名源文件"按钮

### 步骤2：添加文件（关键步骤）
1. **点击"添加文件"按钮**（不要拖拽！）
2. 在文件选择器中选择测试文件
3. **重要**：会弹出第二个对话框要求选择目录
4. 选择包含这些文件的目录（例如：`rename_test`文件夹）
5. 确认文件列表中显示"✓ 可直接重命名"

### 步骤3：设置重命名规则
选择以下任一方式：

**方式A：添加前缀**
- 在"前缀操作"输入框中输入：`[2024]`

**方式B：集数递增（适合视频文件）**
- 勾选"启用自动集数递增"
- 设置起始集数：1
- 设置集数位数：2
- 设置集数前缀：E

**方式C：查找替换**
- 在"查找内容"输入：`test`
- 在"替换为"输入：`renamed`

### 步骤4：验证预览
1. 检查"基本操作效果"预览区域
2. 检查"集数递增效果"预览区域（如果启用）
3. 检查"最终重命名结果"预览区域
4. 确认文件名有实际变化

### 步骤5：执行直接重命名
1. **点击"直接重命名源文件"按钮**
2. 观察按钮变为"重命名中..."
3. 等待操作完成
4. 查看通知消息

### 步骤6：验证结果
1. **打开文件管理器**，导航到测试文件夹
2. **确认源文件名称已被修改**
3. **确认没有生成副本文件**
4. **确认文件内容完整无损**

## 🐛 常见问题排查

### 问题1：看不到"直接重命名源文件"按钮
**原因**：浏览器不支持File System Access API
**解决**：使用Chrome 86+或Edge 86+

### 问题2：文件显示"⚠ 仅下载"
**原因**：文件是通过拖拽添加的，或者没有选择父目录
**解决**：
1. 清空文件列表
2. 使用"添加文件"按钮重新选择
3. 确保选择了包含文件的目录

### 问题3：点击按钮提示"没有可以直接重命名的文件"
**原因**：没有设置重命名规则或文件名没有变化
**解决**：
1. 设置前缀、后缀或查找替换
2. 确认预览中显示文件名有变化

### 问题4：重命名失败
**可能原因**：
- 文件被其他程序占用
- 没有文件系统权限
- 目标文件名已存在

**解决方法**：
1. 关闭可能占用文件的程序
2. 检查文件权限
3. 修改重命名规则避免冲突

## 🔍 调试信息

### 查看控制台日志
1. 按F12打开开发者工具
2. 切换到"Console"标签
3. 执行重命名操作
4. 查看详细的调试信息：
   - 文件选择过程
   - 重命名步骤
   - 错误信息（如果有）

### 关键日志信息
- `开始直接重命名操作`
- `当前文件列表:` - 显示文件状态
- `需要重命名的文件数量:`
- `开始重命名文件:` - 显示具体操作
- `文件重命名完成:`

## ✅ 成功标志

### 功能正常的标志
1. ✅ 浏览器显示支持提示
2. ✅ 文件显示"✓ 可直接重命名"
3. ✅ 预览显示正确的重命名效果
4. ✅ 重命名操作成功完成
5. ✅ 源文件名称确实被修改
6. ✅ 没有生成额外的副本文件

### 测试用例示例

**测试用例1：简单前缀**
- 原文件：`test.txt`
- 前缀：`[2024]`
- 期望结果：`[2024]test.txt`

**测试用例2：电视剧集数**
- 原文件：`episode1.mp4`, `episode2.mp4`
- 启用集数递增：E01, E02
- 期望结果：`episode1.E01.mp4`, `episode2.E02.mp4`

**测试用例3：查找替换**
- 原文件：`old_file.txt`
- 查找：`old`，替换：`new`
- 期望结果：`new_file.txt`

## 🎉 测试完成

如果所有步骤都成功完成，恭喜您！直接重命名功能正常工作。您现在可以：

1. **真正修改源文件名称** - 不需要手动替换
2. **批量重命名多个文件** - 一次操作完成
3. **使用各种重命名规则** - 前缀、后缀、查找替换、集数递增

享受高效的文件重命名体验！🚀
