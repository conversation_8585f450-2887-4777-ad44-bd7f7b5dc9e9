# 🔄 Renamify

A modern, powerful web-based file renaming tool with drag-and-drop support and direct file system access.

![Renamify Interface](https://img.shields.io/badge/Interface-Modern%20Web%20App-blue)
![Browser Support](https://img.shields.io/badge/Browser-Chrome%2086%2B%20%7C%20Edge%2086%2B-green)
![License](https://img.shields.io/badge/License-MIT-yellow)

## ✨ Features

### 🎯 Core Functionality
- **🖱️ Drag & Drop Interface**: Simply drag files into the application
- **⚡ Direct File System Access**: Rename files directly on your system (Chrome 86+)
- **📦 Batch Operations**: Rename multiple files at once with smart filtering
- **🎨 Template System**: Use predefined templates for TV shows, movies, and custom patterns
- **👀 Real-time Preview**: See exactly how files will be renamed before applying changes
- **🌓 Dark/Light Mode**: Automatic theme switching based on system preference

### 🔧 Advanced Features
- **🔍 Smart File Filtering**: Automatically ignores hidden files and duplicates
- **💾 Permission Management**: Intelligent directory permission caching
- **📊 Batch Statistics**: Track selected files and operation results
- **🎭 Multiple Rename Modes**: Direct rename or download renamed copies
- **🔄 Undo Support**: Clear operations and reset permissions easily

## 🌐 Browser Support

| Browser | Direct Rename | Download Mode | Notes |
|---------|---------------|---------------|-------|
| **Chrome 86+** | ✅ Full Support | ✅ | Recommended for best experience |
| **Edge 86+** | ✅ Full Support | ✅ | Full File System Access API support |
| **Firefox** | ❌ | ✅ | Download mode only |
| **Safari** | ❌ | ✅ | Download mode only |

## 🚀 Quick Start

### Option 1: Direct Use
1. Download or clone this repository
2. Open `index.html` in Chrome 86+ or Edge 86+
3. Start renaming files immediately!

### Option 2: GitHub Pages
Visit the live demo: [Renamify on GitHub Pages](https://shakingsafeguard.github.io/Renamify/)

## 📖 Usage Guide

### 1. Adding Files
- **Drag & Drop**: Drag files directly into the drop zone
- **Add Files Button**: Click to select individual files
- **Add Folder Button**: Click to select entire folders

### 2. Setting Up Rename Patterns
- Choose from preset templates (TV Show, Movie, Custom)
- Configure episode numbering and formatting
- Use dynamic placeholders for metadata
- Preview changes in real-time

### 3. Applying Changes
- **Direct Rename**: Modify files directly on your system
- **Download Mode**: Download renamed copies (originals unchanged)

## 🔐 File Access Modes

### 🎯 Direct Rename Mode
- **Requirements**: Chrome 86+ or Edge 86+
- **Permissions**: Requires directory access permission
- **Behavior**: Renames files directly on your file system
- **Benefits**: No downloads, instant results

### 📥 Download Mode
- **Compatibility**: Works in all modern browsers
- **Behavior**: Downloads renamed copies of your files
- **Safety**: Original files remain completely unchanged
- **Use Case**: Perfect for testing or when direct access isn't available

## 🛠️ Development

### Project Structure
```
Renamify/
├── index.html          # Main application (single-file)
├── README.md          # Chinese documentation
├── README_EN.md       # English documentation
└── docs/              # Additional documentation
```

### Local Development
1. Clone the repository
2. Open `index.html` in a modern web browser
3. No build process required - it's a single-file application!

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 🔒 Privacy & Security

- **Local Processing**: All file operations happen locally in your browser
- **No Data Upload**: Files never leave your device
- **Permission Based**: Uses modern browser security APIs
- **Open Source**: Full transparency - inspect the code yourself

## 📝 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request. For major changes, please open an issue first to discuss what you would like to change.

## 📞 Support

If you encounter any issues or have questions:
1. Check the [Issues](https://github.com/ShakingSafeguard/Renamify/issues) page
2. Create a new issue if your problem isn't already reported
3. Provide detailed information about your browser and the issue

---

**Made with ❤️ for efficient file management**
