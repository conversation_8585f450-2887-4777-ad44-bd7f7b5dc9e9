<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Renamify - 智能文件重命名工具</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-hover: #5855eb;
            --secondary-color: #f3f4f6;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        [data-theme="dark"] {
            --primary-color: #818cf8;
            --primary-hover: #6366f1;
            --secondary-color: #374151;
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --border-color: #4b5563;
            --bg-primary: #1f2937;
            --bg-secondary: #111827;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            background: var(--bg-primary);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--shadow);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
        }

        .controls {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            font-size: 14px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
        }

        .theme-toggle {
            background: none;
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            width: 40px;
            height: 40px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .theme-toggle:hover {
            background: var(--secondary-color);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 24px;
            align-items: start;
        }

        .workspace {
            background: var(--bg-primary);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow);
        }

        .sidebar {
            background: var(--bg-primary);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow);
            position: sticky;
            top: 20px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-primary);
        }

        .drop-zone {
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            margin-bottom: 24px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .drop-zone:hover,
        .drop-zone.dragover {
            border-color: var(--primary-color);
            background: rgba(99, 102, 241, 0.05);
        }

        .drop-zone i {
            font-size: 48px;
            color: var(--text-secondary);
            margin-bottom: 16px;
        }

        .file-list {
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 24px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.2s ease;
        }

        .file-item:hover {
            background: var(--secondary-color);
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }

        .file-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-color);
            color: white;
            border-radius: 6px;
            font-size: 14px;
        }

        .file-names {
            flex: 1;
        }

        .original-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .new-name {
            font-size: 12px;
            color: var(--success-color);
            margin-top: 2px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-select {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .input-group {
            display: flex;
            gap: 8px;
        }

        .input-group .form-select {
            flex: 0 0 120px;
        }

        .input-group .form-input {
            flex: 1;
        }

        .preview-container {
            background: var(--secondary-color);
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            border: 1px solid var(--border-color);
        }

        .preview-title {
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-primary);
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .preview-section {
            background: var(--bg-primary);
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 12px;
            border: 1px solid var(--border-color);
        }

        .preview-section:last-child {
            margin-bottom: 0;
        }

        .preview-section-title {
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--text-primary);
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 6px;
            padding-bottom: 6px;
            border-bottom: 1px solid var(--border-color);
        }

        .preview-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: var(--bg-primary);
            border-radius: 6px;
            margin-bottom: 6px;
            font-size: 13px;
        }

        .preview-original {
            color: var(--text-secondary);
            flex: 1;
            margin-right: 12px;
            word-break: break-all;
        }

        .preview-arrow {
            color: var(--primary-color);
            margin: 0 8px;
        }

        .preview-new {
            color: var(--success-color);
            font-weight: 500;
            flex: 1;
            word-break: break-all;
        }

        .form-hint {
            display: block;
            margin-top: 4px;
            font-size: 12px;
            color: var(--text-secondary);
            font-style: italic;
        }

        .regex-controls {
            margin-bottom: 12px;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
            color: var(--text-primary);
        }

        .checkbox-label input[type="checkbox"] {
            display: none;
        }

        .checkmark {
            width: 18px;
            height: 18px;
            border: 2px solid var(--border-color);
            border-radius: 4px;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            position: relative;
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark::after {
            content: '✓';
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .regex-help {
            background: var(--secondary-color);
            border-radius: 6px;
            padding: 12px;
            margin-top: 8px;
            font-size: 12px;
            line-height: 1.4;
            display: none;
        }

        .regex-help.show {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        .regex-example {
            font-family: 'Courier New', monospace;
            background: var(--bg-primary);
            padding: 2px 4px;
            border-radius: 3px;
            margin: 0 2px;
        }

        /* 格式选择器样式 */
        .format-selector {
            margin-bottom: 20px;
        }

        .format-tabs {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }

        .format-tab {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-primary);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 14px;
        }

        .format-tab:hover {
            background: var(--secondary-color);
        }

        .format-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* 模板显示样式 */
        .format-template {
            margin-bottom: 20px;
        }

        .template-preview {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            color: var(--primary-color);
            word-break: break-all;
            margin-top: 8px;
        }

        /* 占位符输入样式 */
        .placeholder-inputs {
            margin-bottom: 20px;
        }

        .placeholder-group {
            margin-bottom: 12px;
        }

        .placeholder-label {
            display: block;
            font-size: 12px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .placeholder-code {
            font-family: 'Courier New', monospace;
            color: var(--primary-color);
            font-size: 11px;
        }

        .placeholder-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 13px;
            transition: all 0.2s ease;
        }

        .placeholder-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
        }

        /* 格式预览样式 */
        .format-preview {
            margin-bottom: 20px;
        }

        .preview-result {
            background: var(--success-color);
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            word-break: break-all;
            margin-top: 8px;
            min-height: 20px;
        }

        .preview-result.empty {
            background: var(--secondary-color);
            color: var(--text-secondary);
            font-family: 'Inter', sans-serif;
            font-style: italic;
        }

        /* 格式操作按钮样式 */
        .format-actions {
            margin-bottom: 20px;
        }

        /* 已保存模板样式 */
        .saved-templates {
            margin-bottom: 20px;
        }

        .template-list {
            max-height: 150px;
            overflow-y: auto;
        }

        .template-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            margin-bottom: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: var(--bg-primary);
        }

        .template-item:hover {
            background: var(--secondary-color);
        }

        .template-item.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .template-info {
            flex: 1;
        }

        .template-name {
            font-weight: 500;
            font-size: 13px;
            margin-bottom: 2px;
        }

        .template-format {
            font-size: 11px;
            font-family: 'Courier New', monospace;
            opacity: 0.8;
        }

        .template-actions {
            display: flex;
            gap: 4px;
        }

        .template-action {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .template-action:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        .template-item.active .template-action {
            color: rgba(255, 255, 255, 0.8);
        }

        .template-item.active .template-action:hover {
            color: white;
        }

        /* 模板管理样式 */
        .template-management {
            display: flex;
            gap: 8px;
        }

        /* 文件管理样式 */
        .file-management {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
        }

        .file-actions {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
            flex-wrap: wrap;
        }

        .file-stats {
            font-size: 14px;
            color: var(--text-secondary);
            padding: 8px 0;
            border-top: 1px solid var(--border-color);
        }

        .file-path {
            font-size: 11px;
            color: var(--text-secondary);
            font-family: 'Courier New', monospace;
            margin-top: 2px;
            word-break: break-all;
        }

        .file-item {
            position: relative;
        }

        .file-item.selected {
            background: rgba(99, 102, 241, 0.1);
            border-color: var(--primary-color);
        }

        .file-item .file-checkbox {
            position: absolute;
            top: 12px;
            right: 40px;
            width: 16px;
            height: 16px;
        }

        .batch-actions {
            background: var(--secondary-color);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            display: none;
        }

        .batch-actions.show {
            display: block;
        }

        .batch-info {
            font-size: 14px;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .auto-episode {
            background: var(--secondary-color);
            border-radius: 8px;
            padding: 12px;
            margin-top: 12px;
        }

        .auto-episode-label {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .episode-settings {
            display: none;
            gap: 12px;
            align-items: center;
            flex-wrap: wrap;
        }

        .episode-settings.show {
            display: flex;
        }

        .episode-input {
            width: 80px;
            padding: 6px 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 13px;
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 8px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .notification.warning {
            border-color: #f59e0b;
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
        }

        .notification.error {
            border-color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }

        .notification.info {
            border-color: var(--primary-color);
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
        }



        .preset-list {
            max-height: 200px;
            overflow-y: auto;
            margin-bottom: 16px;
        }

        .preset-item {
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .preset-item:hover {
            background: var(--secondary-color);
        }

        .preset-item.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .preset-name {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .preset-pattern {
            font-size: 12px;
            color: var(--text-secondary);
            font-family: 'Courier New', monospace;
        }

        .preset-item.active .preset-pattern {
            color: rgba(255, 255, 255, 0.8);
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .sidebar {
                position: static;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 12px;
            }

            .header {
                padding: 16px;
            }

            .workspace,
            .sidebar {
                padding: 16px;
            }

            .controls {
                flex-wrap: wrap;
            }
        }

        .fade-in {
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <i class="fas fa-magic"></i>
                Renamify
            </div>
            <div class="controls">
                <button class="btn btn-secondary" id="undoBtn">
                    <i class="fas fa-undo"></i>
                    撤销
                </button>
                <button class="btn btn-secondary" id="redoBtn">
                    <i class="fas fa-redo"></i>
                    重做
                </button>
                <button class="btn btn-primary" id="directRenameBtn">
                    <i class="fas fa-edit"></i>
                    直接重命名源文件
                </button>
                <button class="btn btn-secondary" id="renameBtn">
                    <i class="fas fa-download"></i>
                    下载重命名文件
                </button>
                <button class="theme-toggle" id="themeToggle">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </header>

        <div class="main-content">
            <div class="workspace">
                <h2 class="section-title">文件工作区</h2>

                <div class="file-management">
                    <div class="file-actions">
                        <button class="btn btn-primary" id="addFilesBtn">
                            <i class="fas fa-plus"></i>
                            添加文件
                        </button>
                        <button class="btn btn-secondary" id="addFolderBtn">
                            <i class="fas fa-folder-plus"></i>
                            添加文件夹
                        </button>
                        <button class="btn btn-secondary" id="clearAllBtn">
                            <i class="fas fa-trash"></i>
                            清空列表
                        </button>
                        <button class="btn btn-secondary" id="resetPermissionsBtn" title="清除目录权限缓存，下次添加文件时重新授权">
                            <i class="fas fa-key"></i>
                            重置权限
                        </button>
                    </div>
                    <div class="file-stats" id="fileStats">
                        <span>已选择 0 个文件</span>
                    </div>
                </div>

                <div class="drop-zone" id="dropZone">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <h3>拖拽文件到这里</h3>
                    <p>或点击上方按钮选择文件/文件夹</p>
                    <input type="file" id="fileInput" multiple style="display: none;">
                    <input type="file" id="folderInput" webkitdirectory multiple style="display: none;">
                </div>

                <div class="batch-actions" id="batchActions">
                    <div class="batch-info">
                        <span id="selectedCount">已选择 0 个文件</span>
                    </div>
                    <div class="file-actions">
                        <button class="btn btn-primary" id="batchRenameBtn">
                            <i class="fas fa-download"></i>
                            下载重命名文件
                        </button>
                        <button class="btn btn-secondary" id="selectAllBtn">
                            <i class="fas fa-check-square"></i>
                            全选
                        </button>
                        <button class="btn btn-secondary" id="deselectAllBtn">
                            <i class="fas fa-square"></i>
                            取消全选
                        </button>
                        <button class="btn btn-danger" id="removeSelectedBtn">
                            <i class="fas fa-trash"></i>
                            移除选中
                        </button>
                    </div>
                </div>

                <div class="file-list" id="fileList"></div>

                <div class="preview-container">
                    <div class="preview-title">
                        <i class="fas fa-eye"></i>
                        实时预览
                    </div>

                    <!-- 基本操作预览 -->
                    <div class="preview-section">
                        <div class="preview-section-title">
                            <i class="fas fa-edit"></i>
                            基本操作效果
                        </div>
                        <div id="basicPreviewArea">
                            <p style="color: var(--text-secondary); text-align: center; padding: 20px;">
                                添加文件后将显示前缀、后缀、查找替换的预览效果
                            </p>
                        </div>
                    </div>

                    <!-- 集数递增预览 -->
                    <div class="preview-section" id="episodePreviewSection" style="display: none;">
                        <div class="preview-section-title">
                            <i class="fas fa-list-ol"></i>
                            集数递增效果
                        </div>
                        <div id="episodePreviewArea">
                            <p style="color: var(--text-secondary); text-align: center; padding: 20px;">
                                启用自动集数递增后将显示集数编号效果
                            </p>
                        </div>
                    </div>

                    <!-- 最终结果预览 -->
                    <div class="preview-section">
                        <div class="preview-section-title">
                            <i class="fas fa-check-circle"></i>
                            最终重命名结果
                        </div>
                        <div id="finalPreviewArea">
                            <p style="color: var(--text-secondary); text-align: center; padding: 20px;">
                                添加文件后将显示最终的重命名结果
                            </p>
                        </div>
                    </div>
                </div>

                <div class="info-panel" style="background: var(--bg-secondary); border-radius: 8px; padding: 16px; margin-top: 16px; border-left: 4px solid var(--primary-color);">
                    <h4 style="margin: 0 0 12px 0; color: var(--primary-color); font-size: 14px;">
                        <i class="fas fa-info-circle"></i> 使用说明
                    </h4>
                    <div style="font-size: 12px; color: var(--text-secondary); line-height: 1.5;">
                        <p style="margin: 0 0 8px 0;">
                            <strong>🎯 直接重命名源文件：</strong>使用"添加文件"按钮选择文件，支持直接修改源文件名称（需要Chrome 86+或Edge 86+）。
                        </p>
                        <p style="margin: 0 0 8px 0;">
                            <strong>📥 下载重命名文件：</strong>如果浏览器不支持直接重命名，可以下载重命名后的文件副本。
                        </p>
                        <p style="margin: 0 0 8px 0;">
                            <strong>👁️ 预览说明：</strong>实时预览分为三个部分 - 基本操作效果、集数递增效果和最终重命名结果。
                        </p>
                        <p style="margin: 0;">
                            <strong>💡 提示：</strong>文件列表中显示"✓ 可直接重命名"的文件支持源文件修改，"⚠ 仅下载"的文件只能下载副本。
                        </p>
                    </div>
                </div>
            </div>

            <div class="sidebar">
                <h2 class="section-title">重命名设置</h2>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-plus-circle"></i>
                        前缀操作
                    </label>
                    <input type="text" class="form-input" id="prefixInput" placeholder="例如：[2024] 或 New_">
                    <small class="form-hint">在文件名开头添加指定内容（可选）</small>
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-plus-circle"></i>
                        后缀操作
                    </label>
                    <input type="text" class="form-input" id="suffixInput" placeholder="例如：_backup 或 _new">
                    <small class="form-hint">在文件名结尾（扩展名前）添加指定内容（可选）</small>
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-search"></i>
                        查找替换操作
                    </label>
                    <div class="regex-controls">
                        <label class="checkbox-label">
                            <input type="checkbox" id="regexMode">
                            <span class="checkmark"></span>
                            启用正则表达式模式
                        </label>
                    </div>
                    <input type="text" class="form-input" id="findInput" placeholder="查找内容（例如：old 或 \d{4}）">
                    <input type="text" class="form-input" id="replaceInput" placeholder="替换为（例如：new 或 2024）" style="margin-top: 8px;">
                    <small class="form-hint" id="regexHint">普通文本查找替换模式</small>
                    <div class="regex-help" id="regexHelp">
                        <strong>常用正则表达式示例：</strong><br>
                        <span class="regex-example">\d+</span> 匹配数字 |
                        <span class="regex-example">.*</span> 匹配任意字符 |
                        <span class="regex-example">[a-z]+</span> 匹配小写字母<br>
                        <span class="regex-example">^</span> 开头 |
                        <span class="regex-example">$</span> 结尾 |
                        <span class="regex-example">\s+</span> 匹配空格
                    </div>
                </div>

                <div class="auto-episode" id="autoEpisode">
                    <h3 class="section-title">
                        <i class="fas fa-list-ol"></i>
                        自动集数递增
                    </h3>
                    <label class="auto-episode-label">
                        <input type="checkbox" id="autoEpisodeCheck">
                        <span>启用自动集数递增</span>
                        <i class="fas fa-info-circle" title="为电视剧自动添加递增的集数编号"></i>
                    </label>
                    <div class="episode-settings" id="episodeSettings">
                        <label>起始集数：
                            <input type="number" id="startEpisode" class="episode-input" value="1" min="1">
                        </label>
                        <label>集数位数：
                            <input type="number" id="episodeDigits" class="episode-input" value="2" min="1" max="4">
                        </label>
                        <label>集数前缀：
                            <input type="text" id="episodePrefix" class="episode-input" value="E" maxlength="5">
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 应用状态管理
        class AppState {
            constructor() {
                this.files = [];
                this.history = [];
                this.historyIndex = -1;
                this.currentPreset = null;
                this.settings = {
                    prefix: '',
                    suffix: '',
                    findPattern: '',
                    replaceWith: '',
                    isRegexMode: false
                };
                this.presets = this.loadPresets();
                this.theme = localStorage.getItem('theme') || 'light';
                this.initTheme();
            }

            initTheme() {
                document.documentElement.setAttribute('data-theme', this.theme);
                const themeIcon = document.querySelector('#themeToggle i');
                themeIcon.className = this.theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }

            toggleTheme() {
                this.theme = this.theme === 'light' ? 'dark' : 'light';
                localStorage.setItem('theme', this.theme);
                this.initTheme();
            }

            loadPresets() {
                const saved = localStorage.getItem('renamify-presets');
                const defaultPresets = [
                    {
                        id: 'tv-show',
                        name: '电视剧格式',
                        pattern: '{name}.S{season:02d}E{episode:02d}.{title}.{quality}.{source}.{codec}',
                        description: '标准电视剧命名格式',
                        settings: {
                            prefix: '',
                            suffix: '',
                            findPattern: '',
                            replaceWith: ''
                        }
                    },
                    {
                        id: 'movie',
                        name: '电影格式',
                        pattern: '{title}.{year}.{quality}.{source}.{codec}-{group}',
                        description: '标准电影命名格式',
                        settings: {
                            prefix: '',
                            suffix: '',
                            findPattern: '',
                            replaceWith: ''
                        }
                    },
                    {
                        id: 'date-prefix',
                        name: '日期前缀',
                        pattern: '{date}_',
                        description: '添加当前日期作为前缀',
                        settings: {
                            prefix: new Date().toISOString().split('T')[0] + '_',
                            suffix: '',
                            findPattern: '',
                            replaceWith: ''
                        }
                    }
                ];

                return saved ? [...defaultPresets, ...JSON.parse(saved)] : defaultPresets;
            }

            savePresets() {
                const customPresets = this.presets.filter(p => !['tv-show', 'movie', 'date-prefix'].includes(p.id));
                localStorage.setItem('renamify-presets', JSON.stringify(customPresets));
            }

            addToHistory(action) {
                this.history = this.history.slice(0, this.historyIndex + 1);
                this.history.push(action);
                this.historyIndex++;
                this.updateHistoryButtons();
            }

            undo() {
                if (this.historyIndex >= 0) {
                    const action = this.history[this.historyIndex];
                    this.executeAction(action, true);
                    this.historyIndex--;
                    this.updateHistoryButtons();
                }
            }

            redo() {
                if (this.historyIndex < this.history.length - 1) {
                    this.historyIndex++;
                    const action = this.history[this.historyIndex];
                    this.executeAction(action, false);
                    this.updateHistoryButtons();
                }
            }

            updateHistoryButtons() {
                document.getElementById('undoBtn').disabled = this.historyIndex < 0;
                document.getElementById('redoBtn').disabled = this.historyIndex >= this.history.length - 1;
            }

            executeAction(action, isUndo) {
                // 实现撤销/重做逻辑
                console.log('Execute action:', action, 'isUndo:', isUndo);
            }
        }



        // 初始化应用
        const app = new AppState();

        // 检查浏览器兼容性
        function checkBrowserCompatibility() {
            const fileManager = new FileManager();
            const supportsDirectRename = fileManager.supportsFileSystemAccess();

            if (supportsDirectRename) {
                // 显示支持直接重命名的提示
                const directRenameBtn = document.getElementById('directRenameBtn');
                directRenameBtn.style.display = 'flex';

                // 添加成功提示和使用指导
                setTimeout(() => {
                    fileManager.showNotification('✅ 您的浏览器支持直接文件重命名功能！\n\n📝 使用步骤：\n1. 点击"添加文件"按钮选择文件\n2. 选择包含文件的目录\n3. 设置重命名规则\n4. 点击"直接重命名源文件"', 'success');
                }, 1000);
            } else {
                // 隐藏直接重命名按钮
                const directRenameBtn = document.getElementById('directRenameBtn');
                directRenameBtn.style.display = 'none';

                // 添加提示
                setTimeout(() => {
                    fileManager.showNotification('⚠️ 您的浏览器不支持直接重命名，建议使用Chrome 86+或Edge 86+', 'warning');
                }, 1000);
            }
        }

        // DOM 元素引用
        const elements = {
            dropZone: document.getElementById('dropZone'),
            fileInput: document.getElementById('fileInput'),
            fileList: document.getElementById('fileList'),
            prefixInput: document.getElementById('prefixInput'),
            suffixInput: document.getElementById('suffixInput'),
            findInput: document.getElementById('findInput'),
            replaceInput: document.getElementById('replaceInput'),
            regexMode: document.getElementById('regexMode'),
            regexHint: document.getElementById('regexHint'),
            regexHelp: document.getElementById('regexHelp'),
            previewArea: document.getElementById('previewArea'),
            themeToggle: document.getElementById('themeToggle'),
            renameBtn: document.getElementById('renameBtn'),
            undoBtn: document.getElementById('undoBtn'),
            redoBtn: document.getElementById('redoBtn')
        };

        // 文件管理类
        class FileManager {
            constructor() {
                this.fileHandles = new Map(); // 存储文件句柄
                this.directoryHandle = null; // 存储目录句柄
                this.setupEventListeners();
            }

            setupEventListeners() {
                // 拖拽事件
                elements.dropZone.addEventListener('dragover', this.handleDragOver.bind(this));
                elements.dropZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
                elements.dropZone.addEventListener('drop', this.handleDrop.bind(this));

                // 拖拽区域点击事件（只在点击空白区域时触发）
                elements.dropZone.addEventListener('click', async (e) => {
                    // 如果点击的是按钮或其子元素，不触发文件选择
                    if (e.target.closest('button') || e.target.closest('.btn')) {
                        return;
                    }

                    // 如果支持File System Access API，优先使用
                    if (this.supportsFileSystemAccess()) {
                        try {
                            await this.addFilesWithSystemAccess();
                            return;
                        } catch (error) {
                            if (error.name !== 'AbortError') {
                                console.warn('File System Access API 失败，回退到传统方式:', error);
                                this.showNotification('文件选择失败，请重试', 'warning');
                            }
                            // 不要回退到传统方式，让用户明确知道需要重试
                            return;
                        }
                    }

                    // 回退到传统文件选择方式
                    const tempInput = document.createElement('input');
                    tempInput.type = 'file';
                    tempInput.multiple = true;
                    tempInput.style.display = 'none';

                    tempInput.addEventListener('change', (e) => {
                        const files = Array.from(e.target.files);
                        this.addFiles(files);
                        document.body.removeChild(tempInput);
                    });

                    document.body.appendChild(tempInput);
                    tempInput.click();
                });

                // 文件选择事件
                elements.fileInput.addEventListener('change', this.handleFileSelect.bind(this));

                // 文件夹选择事件
                const folderInput = document.getElementById('folderInput');
                folderInput.addEventListener('change', this.handleFileSelect.bind(this));

                // 文件管理按钮事件
                document.getElementById('addFilesBtn').addEventListener('click', async (e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    if (this.supportsFileSystemAccess()) {
                        try {
                            await this.addFilesWithSystemAccess();
                        } catch (error) {
                            if (error.name !== 'AbortError') {
                                console.warn('File System Access API 失败，回退到传统方式:', error);
                                this.showNotification('文件选择失败，请重试', 'error');
                            }
                        }
                    } else {
                        // 创建临时的文件输入框，避免与现有的冲突
                        const tempInput = document.createElement('input');
                        tempInput.type = 'file';
                        tempInput.multiple = true;
                        tempInput.style.display = 'none';

                        tempInput.addEventListener('change', (e) => {
                            const files = Array.from(e.target.files);
                            this.addFiles(files);
                            document.body.removeChild(tempInput);
                        });

                        document.body.appendChild(tempInput);
                        tempInput.click();
                    }
                });

                document.getElementById('addFolderBtn').addEventListener('click', async (e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    if (this.supportsFileSystemAccess()) {
                        try {
                            await this.addFolderWithSystemAccess();
                        } catch (error) {
                            if (error.name !== 'AbortError') {
                                console.warn('File System Access API 失败，回退到传统方式:', error);
                                this.showNotification('文件夹选择失败，请重试', 'error');
                            }
                        }
                    } else {
                        // 创建临时的文件夹输入框
                        const tempInput = document.createElement('input');
                        tempInput.type = 'file';
                        tempInput.webkitdirectory = true;
                        tempInput.multiple = true;
                        tempInput.style.display = 'none';

                        tempInput.addEventListener('change', (e) => {
                            const files = Array.from(e.target.files);
                            this.addFiles(files);
                            document.body.removeChild(tempInput);
                        });

                        document.body.appendChild(tempInput);
                        tempInput.click();
                    }
                });

                document.getElementById('clearAllBtn').addEventListener('click', () => {
                    this.clearAllFiles();
                });

                document.getElementById('resetPermissionsBtn').addEventListener('click', () => {
                    this.resetPermissions();
                });

                document.getElementById('selectAllBtn').addEventListener('click', () => {
                    this.selectAllFiles();
                });

                document.getElementById('deselectAllBtn').addEventListener('click', () => {
                    this.deselectAllFiles();
                });

                document.getElementById('removeSelectedBtn').addEventListener('click', () => {
                    this.removeSelectedFiles();
                });

                document.getElementById('batchRenameBtn').addEventListener('click', () => {
                    this.batchRename();
                });

                // 直接重命名按钮
                document.getElementById('directRenameBtn').addEventListener('click', () => {
                    this.directRename();
                });
            }

            handleDragOver(e) {
                e.preventDefault();
                elements.dropZone.classList.add('dragover');
            }

            handleDragLeave(e) {
                e.preventDefault();
                elements.dropZone.classList.remove('dragover');
            }

            async handleDrop(e) {
                e.preventDefault();
                elements.dropZone.classList.remove('dragover');

                const files = Array.from(e.dataTransfer.files);

                // 过滤掉隐藏文件
                const visibleFiles = files.filter(file => !file.name.startsWith('.'));

                if (visibleFiles.length === 0) {
                    this.showNotification('没有可添加的文件（已忽略隐藏文件）', 'warning');
                    return;
                }

                // 检查是否有重复文件
                const newFiles = this.filterDuplicateFiles(visibleFiles);

                if (newFiles.length === 0) {
                    this.showNotification('所有文件都已存在于列表中', 'info');
                    return;
                }

                // 如果支持File System Access API且没有现有的目录句柄，询问用户是否要启用直接重命名
                if (this.supportsFileSystemAccess() && !this.directoryHandle && newFiles.length > 0) {
                    try {
                        this.showNotification('检测到拖拽文件，是否选择目录以启用直接重命名？', 'info');

                        // 给用户选择是否要启用直接重命名
                        const userChoice = await this.askForDirectoryPermission();
                        if (userChoice) {
                            const directoryHandle = await window.showDirectoryPicker();
                            this.directoryHandle = directoryHandle;

                            // 将拖拽的文件转换为支持直接重命名的文件
                            await this.addDraggedFilesWithDirectRename(newFiles, directoryHandle);
                            return;
                        }
                    } catch (error) {
                        console.log('用户选择不启用直接重命名或操作被取消');
                    }
                }

                // 如果已有目录句柄，验证其有效性后尝试直接使用
                if (this.directoryHandle && newFiles.length > 0) {
                    try {
                        // 验证目录句柄是否仍然有效
                        await this.directoryHandle.queryPermission({ mode: 'readwrite' });
                        await this.addDraggedFilesWithDirectRename(newFiles, this.directoryHandle);
                        return;
                    } catch (error) {
                        console.warn('使用现有目录句柄失败，清除并回退到传统方式:', error);
                        this.directoryHandle = null;
                    }
                }

                // 回退到传统方式
                this.addFiles(newFiles);
            }

            handleFileSelect(e) {
                const files = Array.from(e.target.files);
                this.addFiles(files);
                // 重置输入框，允许重新选择相同文件
                e.target.value = '';
            }

            // 过滤重复文件的方法
            filterDuplicateFiles(files) {
                return files.filter(file => {
                    // 创建唯一标识符
                    const filePath = file.webkitRelativePath || file.name;
                    const uniqueId = filePath + '_' + file.size + '_' + file.lastModified;

                    // 检查是否已存在
                    const exists = app.files.some(f => f.uniqueId === uniqueId);

                    if (exists) {
                        console.log('文件已存在，跳过:', file.name);
                        return false;
                    }

                    return true;
                });
            }

            supportsFileSystemAccess() {
                return 'showOpenFilePicker' in window && 'showDirectoryPicker' in window;
            }

            async askForDirectoryPermission() {
                return new Promise((resolve) => {
                    // 创建确认对话框
                    const modal = document.createElement('div');
                    modal.style.cssText = `
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0,0,0,0.5);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 10000;
                    `;

                    modal.innerHTML = `
                        <div style="background: var(--bg-primary); padding: 24px; border-radius: 12px; max-width: 400px; text-align: center;">
                            <h3 style="margin: 0 0 16px 0; color: var(--text-primary);">启用直接重命名？</h3>
                            <p style="margin: 0 0 20px 0; color: var(--text-secondary); line-height: 1.5;">
                                检测到拖拽文件。选择目录可以启用直接重命名功能，否则只能下载重命名后的文件。
                            </p>
                            <div style="display: flex; gap: 12px; justify-content: center;">
                                <button id="enableDirectRename" style="padding: 8px 16px; background: var(--primary-color); color: white; border: none; border-radius: 6px; cursor: pointer;">
                                    启用直接重命名
                                </button>
                                <button id="skipDirectRename" style="padding: 8px 16px; background: var(--secondary-color); color: var(--text-primary); border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer;">
                                    仅下载模式
                                </button>
                            </div>
                        </div>
                    `;

                    document.body.appendChild(modal);

                    // 事件处理
                    modal.querySelector('#enableDirectRename').onclick = () => {
                        document.body.removeChild(modal);
                        resolve(true);
                    };

                    modal.querySelector('#skipDirectRename').onclick = () => {
                        document.body.removeChild(modal);
                        resolve(false);
                    };

                    // 点击背景关闭
                    modal.onclick = (e) => {
                        if (e.target === modal) {
                            document.body.removeChild(modal);
                            resolve(false);
                        }
                    };

                    // 5秒后自动选择仅下载模式
                    setTimeout(() => {
                        if (document.body.contains(modal)) {
                            document.body.removeChild(modal);
                            resolve(false);
                        }
                    }, 5000);
                });
            }

            async addDraggedFilesWithDirectRename(files, directoryHandle) {
                let addedCount = 0;
                let directRenameCount = 0;
                let downloadOnlyCount = 0;
                let skippedCount = 0;

                for (const file of files) {
                    // 检查是否为重复文件
                    const filePath = file.webkitRelativePath || file.name;
                    const uniqueId = filePath + '_' + file.size + '_' + file.lastModified;

                    if (app.files.some(f => f.uniqueId === uniqueId)) {
                        console.log('文件已存在，跳过:', file.name);
                        skippedCount++;
                        continue;
                    }

                    try {
                        // 验证文件是否在选择的目录中
                        const fileHandle = await directoryHandle.getFileHandle(file.name);

                        // 生成稳定的ID
                        const fileId = 'file_' + Date.now() + '_' + Math.floor(Math.random() * 10000);
                        const fileObj = {
                            id: fileId,
                            uniqueId: uniqueId,
                            name: file.name,
                            originalName: file.name,
                            path: file.webkitRelativePath ? file.webkitRelativePath.replace(file.name, '').replace(/\/$/, '') : '',
                            size: file.size,
                            type: file.type,
                            file: file,
                            fileHandle: fileHandle,
                            parentDirectoryHandle: directoryHandle,
                            selected: false,
                            canDirectRename: true
                        };

                        // 存储文件句柄映射
                        this.fileHandles.set(fileId, fileHandle);
                        app.files.push(fileObj);
                        console.log('添加拖拽文件（支持直接重命名），ID:', fileId, '文件名:', file.name);
                        addedCount++;
                        directRenameCount++;

                    } catch (error) {
                        console.warn(`文件 ${file.name} 不在选择的目录中，使用下载模式`);
                        // 如果文件不在目录中，回退到传统模式
                        const fileId = 'file_' + Date.now() + '_' + Math.floor(Math.random() * 10000);
                        const fileObj = {
                            id: fileId,
                            uniqueId: uniqueId,
                            name: file.name,
                            originalName: file.name,
                            path: file.webkitRelativePath ? file.webkitRelativePath.replace(file.name, '').replace(/\/$/, '') : '',
                            size: file.size,
                            type: file.type,
                            file: file,
                            selected: false,
                            canDirectRename: false
                        };
                        app.files.push(fileObj);
                        addedCount++;
                        downloadOnlyCount++;
                    }
                }

                this.renderFileList();
                renameEngine.updatePreview();
                renameEngine.updatePreviewArea();

                let message = `已添加 ${addedCount} 个文件`;
                if (directRenameCount > 0) {
                    message += `（${directRenameCount} 个支持直接重命名`;
                    if (downloadOnlyCount > 0) {
                        message += `，${downloadOnlyCount} 个仅支持下载`;
                    }
                    message += '）';
                }
                if (skippedCount > 0) {
                    message += `，跳过了 ${skippedCount} 个重复文件`;
                }

                this.showNotification(message, 'success');
            }

            async addFilesWithSystemAccess() {
                try {
                    const fileHandles = await window.showOpenFilePicker({
                        multiple: true,
                        excludeAcceptAllOption: false
                    });

                    // 智能目录选择：优先使用已有的目录句柄
                    let parentDirectoryHandle = this.directoryHandle;

                    // 验证现有目录句柄是否仍然有效
                    if (parentDirectoryHandle) {
                        try {
                            // 尝试访问目录句柄来验证其有效性
                            await parentDirectoryHandle.queryPermission({ mode: 'readwrite' });
                            console.log('使用已有的目录句柄:', parentDirectoryHandle.name);
                        } catch (error) {
                            console.warn('现有目录句柄无效，清除:', error);
                            parentDirectoryHandle = null;
                            this.directoryHandle = null;
                        }
                    }

                    if (!parentDirectoryHandle && fileHandles.length > 0) {
                        try {
                            // 只在没有目录句柄时才提示用户选择
                            this.showNotification('请选择包含所选文件的目录以启用直接重命名功能', 'info');
                            parentDirectoryHandle = await window.showDirectoryPicker();
                            this.directoryHandle = parentDirectoryHandle;
                            console.log('获取新的目录句柄:', parentDirectoryHandle.name);
                        } catch (error) {
                            if (error.name !== 'AbortError') {
                                console.warn('无法获取父目录句柄:', error);
                            }
                            // 即使没有父目录句柄，文件仍然可以添加，只是不能直接重命名
                        }
                    }

                    let addedCount = 0;
                    let skippedCount = 0;

                    for (const fileHandle of fileHandles) {
                        const file = await fileHandle.getFile();

                        // 检查是否为重复文件
                        const uniqueId = fileHandle.name + '_' + file.size + '_' + file.lastModified;

                        if (app.files.some(f => f.uniqueId === uniqueId)) {
                            console.log('文件已存在，跳过:', file.name);
                            skippedCount++;
                            continue;
                        }

                        // 生成更稳定的ID
                        const fileId = 'file_' + Date.now() + '_' + Math.floor(Math.random() * 10000);
                        const fileObj = {
                            id: fileId,
                            uniqueId: uniqueId,
                            name: file.name,
                            originalName: file.name,
                            path: '',
                            size: file.size,
                            type: file.type,
                            file: file,
                            fileHandle: fileHandle, // 保存文件句柄用于直接重命名
                            parentDirectoryHandle: parentDirectoryHandle, // 保存父目录句柄
                            selected: false,
                            canDirectRename: !!parentDirectoryHandle // 只有有父目录句柄才能直接重命名
                        };

                        // 存储文件句柄映射
                        this.fileHandles.set(fileId, fileHandle);
                        app.files.push(fileObj);
                        console.log('添加文件，ID:', fileId, '文件名:', file.name);
                        addedCount++;
                    }

                    this.renderFileList();
                    renameEngine.updatePreview();
                    renameEngine.updatePreviewArea();

                    if (addedCount === 0) {
                        this.showNotification('所有文件都已存在于列表中', 'info');
                        return;
                    }

                    let message = parentDirectoryHandle
                        ? `已添加 ${addedCount} 个文件（支持直接重命名）`
                        : `已添加 ${addedCount} 个文件（仅支持下载重命名）`;

                    if (skippedCount > 0) {
                        message += `，跳过了 ${skippedCount} 个重复文件`;
                    }

                    this.showNotification(message, 'success');
                } catch (error) {
                    if (error.name !== 'AbortError') {
                        this.showNotification('添加文件失败: ' + error.message, 'error');
                    }
                }
            }

            async addFolderWithSystemAccess() {
                try {
                    const directoryHandle = await window.showDirectoryPicker();

                    // 验证目录权限
                    const permission = await directoryHandle.queryPermission({ mode: 'readwrite' });
                    if (permission !== 'granted') {
                        const requestPermission = await directoryHandle.requestPermission({ mode: 'readwrite' });
                        if (requestPermission !== 'granted') {
                            throw new Error('需要目录读写权限才能启用直接重命名功能');
                        }
                    }

                    this.directoryHandle = directoryHandle;

                    const files = await this.getAllFilesFromDirectory(directoryHandle, '', directoryHandle);

                    let addedCount = 0;
                    let skippedCount = 0;

                    for (const { file, handle, path, parentHandle } of files) {
                        // 检查是否为重复文件
                        const uniqueId = path + '_' + file.size + '_' + file.lastModified;

                        if (app.files.some(f => f.uniqueId === uniqueId)) {
                            console.log('文件已存在，跳过:', file.name);
                            skippedCount++;
                            continue;
                        }

                        // 生成稳定的ID
                        const fileId = 'file_' + Date.now() + '_' + Math.floor(Math.random() * 10000);
                        const fileObj = {
                            id: fileId,
                            uniqueId: uniqueId,
                            name: file.name,
                            originalName: file.name,
                            path: path.replace(file.name, '').replace(/\/$/, ''),
                            size: file.size,
                            type: file.type,
                            file: file,
                            fileHandle: handle,
                            parentDirectoryHandle: parentHandle, // 存储父目录句柄
                            selected: false,
                            canDirectRename: true
                        };

                        this.fileHandles.set(fileId, handle);
                        app.files.push(fileObj);
                        console.log('添加文件夹文件，ID:', fileId, '文件名:', file.name);
                        addedCount++;
                    }

                    this.renderFileList();
                    renameEngine.updatePreview();
                    renameEngine.updatePreviewArea();

                    if (addedCount === 0) {
                        this.showNotification('所有文件都已存在于列表中', 'info');
                        return;
                    }

                    let message = `已添加 ${addedCount} 个文件（支持直接重命名）`;
                    if (skippedCount > 0) {
                        message += `，跳过了 ${skippedCount} 个重复文件`;
                    }

                    this.showNotification(message, 'success');
                } catch (error) {
                    if (error.name !== 'AbortError') {
                        this.showNotification('添加文件夹失败: ' + error.message, 'error');
                    }
                }
            }

            async getAllFilesFromDirectory(directoryHandle, path = '', rootHandle = null) {
                const files = [];
                const currentRootHandle = rootHandle || directoryHandle;

                for await (const [name, handle] of directoryHandle.entries()) {
                    // 忽略隐藏文件和文件夹（以.开头的文件）
                    if (name.startsWith('.')) {
                        console.log('忽略隐藏文件/文件夹:', name);
                        continue;
                    }

                    const currentPath = path ? `${path}/${name}` : name;

                    if (handle.kind === 'file') {
                        const file = await handle.getFile();
                        files.push({
                            file,
                            handle,
                            path: currentPath,
                            parentHandle: directoryHandle // 直接父目录句柄
                        });
                    } else if (handle.kind === 'directory') {
                        const subFiles = await this.getAllFilesFromDirectory(handle, currentPath, currentRootHandle);
                        files.push(...subFiles);
                    }
                }

                return files;
            }

            addFiles(files) {
                // 过滤隐藏文件和重复文件
                const visibleFiles = files.filter(file => !file.name.startsWith('.'));
                const newFiles = this.filterDuplicateFiles(visibleFiles);

                if (newFiles.length === 0) {
                    if (visibleFiles.length === 0) {
                        this.showNotification('没有可添加的文件（已忽略隐藏文件）', 'warning');
                    } else {
                        this.showNotification('所有文件都已存在于列表中', 'info');
                    }
                    return;
                }

                newFiles.forEach(file => {
                    // 创建唯一标识符，包含路径信息
                    const filePath = file.webkitRelativePath || file.name;
                    const uniqueId = filePath + '_' + file.size + '_' + file.lastModified;

                    // 生成稳定的ID
                    const fileId = 'file_' + Date.now() + '_' + Math.floor(Math.random() * 10000);
                    const fileObj = {
                        id: fileId,
                        uniqueId: uniqueId,
                        name: file.name,
                        originalName: file.name,
                        path: file.webkitRelativePath ? file.webkitRelativePath.replace(file.name, '').replace(/\/$/, '') : '',
                        size: file.size,
                        type: file.type,
                        file: file,
                        selected: false,
                        canDirectRename: false // 传统方式添加的文件不支持直接重命名
                    };
                    app.files.push(fileObj);
                    console.log('添加文件（传统模式），ID:', fileId, '文件名:', file.name);
                });

                this.renderFileList();
                renameEngine.updatePreview();
                renameEngine.updatePreviewArea();

                const hiddenCount = files.length - visibleFiles.length;
                const duplicateCount = visibleFiles.length - newFiles.length;

                let message = `已添加 ${newFiles.length} 个文件`;
                if (hiddenCount > 0) {
                    message += `（忽略了 ${hiddenCount} 个隐藏文件）`;
                }
                if (duplicateCount > 0) {
                    message += `（跳过了 ${duplicateCount} 个重复文件）`;
                }

                this.showNotification(message, 'success');
            }

            removeFile(fileId) {
                const index = app.files.findIndex(f => f.id == fileId);
                if (index !== -1) {
                    app.files.splice(index, 1);
                    this.renderFileList();
                    renameEngine.updatePreview();
                    renameEngine.updatePreviewArea();
                    this.showNotification('文件已移除', 'success');
                }
            }

            renderFileList() {
                if (app.files.length === 0) {
                    elements.fileList.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 20px;">暂无文件</p>';
                    this.updateFileStats();
                    return;
                }

                elements.fileList.innerHTML = app.files.map(file => `
                    <div class="file-item fade-in ${file.selected ? 'selected' : ''}" data-file-id="${file.id}">
                        <div class="file-info">
                            <div class="file-icon">
                                <i class="fas ${this.getFileIcon(file.type)}"></i>
                            </div>
                            <div class="file-names">
                                <div class="original-name">
                                    ${this.escapeHtml(file.originalName)}
                                    ${file.canDirectRename ? '<span style="color: var(--success-color); font-size: 11px; margin-left: 8px;">✓ 可直接重命名</span>' : '<span style="color: var(--warning-color); font-size: 11px; margin-left: 8px;">⚠ 仅下载</span>'}
                                </div>
                                <div class="file-path">${this.escapeHtml(file.path || '')}</div>
                                <div class="new-name" id="preview-${file.id}"></div>
                            </div>
                        </div>
                        <input type="checkbox" class="file-checkbox" ${file.selected ? 'checked' : ''}
                               onchange="fileManager.toggleFileSelection('${file.id}', this.checked)">
                        <button class="btn btn-secondary" onclick="fileManager.removeFile('${file.id}')" style="padding: 8px;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `).join('');

                this.updateFileStats();
            }

            updateFileStats() {
                const total = app.files.length;
                const selected = app.files.filter(f => f.selected).length;

                document.getElementById('fileStats').innerHTML = `
                    <span>已选择 ${total} 个文件</span>
                `;

                document.getElementById('selectedCount').textContent = `已选择 ${selected} 个文件`;

                const batchActions = document.getElementById('batchActions');
                if (selected > 0) {
                    batchActions.classList.add('show');
                } else {
                    batchActions.classList.remove('show');
                }
            }

            toggleFileSelection(fileId, selected) {
                const file = app.files.find(f => f.id == fileId);
                if (file) {
                    file.selected = selected;
                    this.renderFileList();
                }
            }

            selectAllFiles() {
                app.files.forEach(file => file.selected = true);
                this.renderFileList();
            }

            deselectAllFiles() {
                app.files.forEach(file => file.selected = false);
                this.renderFileList();
            }

            removeSelectedFiles() {
                app.files = app.files.filter(f => !f.selected);
                this.renderFileList();
                renameEngine.updatePreview();
                renameEngine.updatePreviewArea();
            }

            clearAllFiles() {
                if (app.files.length === 0) {
                    this.showNotification('列表已经是空的', 'info');
                    return;
                }

                if (confirm('确定要清空所有文件吗？')) {
                    app.files = [];
                    // 清空文件句柄映射
                    this.fileHandles.clear();
                    // 清空目录句柄，下次添加文件时重新授权
                    this.directoryHandle = null;

                    this.renderFileList();
                    renameEngine.updatePreview();
                    renameEngine.updatePreviewArea();
                    this.showNotification('已清空文件列表和权限缓存', 'success');

                    // 重置文件输入框，确保可以重新选择相同文件
                    elements.fileInput.value = '';
                    const folderInput = document.getElementById('folderInput');
                    if (folderInput) {
                        folderInput.value = '';
                    }
                }
            }

            resetPermissions() {
                if (confirm('确定要重置目录权限缓存吗？下次添加文件时需要重新选择目录。')) {
                    // 清空目录句柄
                    this.directoryHandle = null;

                    // 将现有的支持直接重命名的文件改为仅下载模式
                    app.files.forEach(file => {
                        if (file.canDirectRename) {
                            file.canDirectRename = false;
                            // 清除文件句柄
                            this.fileHandles.delete(file.id);
                            file.fileHandle = null;
                            file.parentDirectoryHandle = null;
                        }
                    });

                    this.renderFileList();
                    this.showNotification('已重置权限缓存，现有文件已切换为仅下载模式', 'success');
                }
            }

            async batchRename() {
                const selectedFiles = app.files.filter(f => f.selected);
                if (selectedFiles.length === 0) {
                    this.showNotification('请先选择要重命名的文件', 'warning');
                    return;
                }

                // 检查是否有文件需要重命名
                const filesToRename = selectedFiles.filter(file => {
                    const newName = file.newName || file.originalName;
                    return newName !== file.originalName;
                });

                if (filesToRename.length === 0) {
                    this.showNotification('所选文件无需重命名', 'info');
                    return;
                }

                const button = document.getElementById('batchRenameBtn');
                const originalText = button.innerHTML;
                button.innerHTML = '<div class="loading"></div> 处理中...';
                button.disabled = true;

                try {
                    // 由于浏览器安全限制，我们提供下载重命名后文件的功能
                    await this.downloadRenamedFiles(filesToRename);

                    this.showNotification(`已为 ${filesToRename.length} 个文件生成重命名版本`, 'success');

                    // 更新文件名显示
                    filesToRename.forEach(file => {
                        file.originalName = file.newName || file.originalName;
                        file.selected = false;
                    });

                    this.renderFileList();
                    renameEngine.updatePreview();
                    renameEngine.updatePreviewArea();

                } catch (error) {
                    this.showNotification('处理失败: ' + error.message, 'error');
                } finally {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }

            async directRename() {
                console.log('开始直接重命名操作');

                if (!this.supportsFileSystemAccess()) {
                    this.showNotification('您的浏览器不支持直接文件重命名功能，请使用Chrome 86+或Edge 86+', 'warning');
                    return;
                }

                // 确保预览已更新，生成最新的newName
                renameEngine.updatePreview();

                // 调试信息：显示所有文件的状态
                console.log('当前文件列表:', app.files.map(f => ({
                    name: f.originalName,
                    newName: f.newName,
                    canDirectRename: f.canDirectRename,
                    hasFileHandle: !!this.fileHandles.get(f.id),
                    hasParentHandle: !!f.parentDirectoryHandle
                })));

                const filesToRename = app.files.filter(f => {
                    const hasNewName = f.newName && f.newName !== f.originalName;
                    const canRename = f.canDirectRename;
                    console.log(`文件 ${f.originalName}: hasNewName=${hasNewName}, canRename=${canRename}`);
                    return canRename && hasNewName;
                });

                console.log('需要重命名的文件数量:', filesToRename.length);

                if (filesToRename.length === 0) {
                    this.showNotification('没有可以直接重命名的文件。请确保：\n1. 通过"添加文件"按钮选择文件\n2. 设置了重命名规则\n3. 文件名有实际变化', 'warning');
                    return;
                }

                const button = document.getElementById('directRenameBtn');
                const originalText = button.innerHTML;
                button.innerHTML = '<div class="loading"></div> 重命名中...';
                button.disabled = true;

                let successCount = 0;
                let errorCount = 0;
                const errors = [];

                try {
                    for (const file of filesToRename) {
                        try {
                            await this.renameFileDirectly(file);
                            successCount++;

                            // 更新文件对象 - 重命名成功后，更新当前文件名
                            file.originalName = file.newName;  // 更新为新的文件名
                            file.name = file.newName;
                            file.newName = null;  // 清空newName，表示已完成重命名

                        } catch (error) {
                            errorCount++;
                            errors.push(`${file.originalName}: ${error.message}`);
                            console.error('重命名失败:', file.originalName, error);
                        }
                    }

                    // 显示结果
                    if (successCount > 0) {
                        this.showNotification(`成功重命名 ${successCount} 个文件`, 'success');

                        // 重新渲染文件列表
                        this.renderFileList();
                        renameEngine.updatePreview();
                        renameEngine.updatePreviewArea();
                    }

                    if (errorCount > 0) {
                        const errorMsg = `${errorCount} 个文件重命名失败:\n${errors.slice(0, 3).join('\n')}${errors.length > 3 ? '\n...' : ''}`;
                        this.showNotification(errorMsg, 'error');
                    }

                } catch (error) {
                    this.showNotification('重命名过程中发生错误: ' + error.message, 'error');
                } finally {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }

            async renameFileDirectly(fileObj) {
                console.log('开始重命名文件:', fileObj.originalName, '→', fileObj.newName);
                console.log('文件对象详情:', {
                    id: fileObj.id,
                    originalName: fileObj.originalName,
                    name: fileObj.name,
                    newName: fileObj.newName,
                    hasFileHandle: !!this.fileHandles.get(fileObj.id),
                    hasParentHandle: !!fileObj.parentDirectoryHandle
                });

                const fileHandle = this.fileHandles.get(fileObj.id);
                if (!fileHandle) {
                    console.error('文件句柄映射:', Array.from(this.fileHandles.keys()));
                    throw new Error('文件句柄不存在，请重新选择文件');
                }

                const newName = fileObj.newName;
                if (!newName || newName === fileObj.originalName) {
                    throw new Error('新文件名无效');
                }

                try {
                    // 检查文件句柄是否仍然有效
                    try {
                        await fileHandle.getFile();
                    } catch (error) {
                        throw new Error('文件句柄已失效，请重新选择文件');
                    }

                    // 方法1: 尝试使用move方法（目前大多数浏览器还不支持）
                    if (fileHandle.move && typeof fileHandle.move === 'function') {
                        console.log('使用move方法重命名');
                        await fileHandle.move(newName);
                        console.log('move方法重命名成功');
                        return;
                    }

                    // 方法2: 使用创建新文件并删除旧文件的方式
                    console.log('使用创建新文件方法重命名');

                    // 获取文件内容
                    const file = await fileHandle.getFile();
                    console.log('获取文件内容成功，大小:', file.size);

                    // 获取父目录句柄
                    const parentHandle = await this.getParentDirectoryHandle(fileObj);
                    if (!parentHandle) {
                        throw new Error('无法获取父目录句柄，请重新选择目录');
                    }

                    console.log('获取父目录句柄成功');

                    // 检查新文件名是否已存在
                    try {
                        await parentHandle.getFileHandle(newName);
                        throw new Error(`文件名 "${newName}" 已存在`);
                    } catch (error) {
                        if (error.name !== 'NotFoundError') {
                            throw error;
                        }
                        // NotFoundError是期望的，表示文件不存在，可以创建
                    }

                    // 创建新文件
                    console.log('创建新文件:', newName);
                    const newFileHandle = await parentHandle.getFileHandle(newName, { create: true });

                    // 写入文件内容
                    console.log('写入文件内容');
                    const writable = await newFileHandle.createWritable();
                    await writable.write(file);
                    await writable.close();
                    console.log('文件内容写入完成');

                    // 删除旧文件 - 使用当前的文件名（可能已经被重命名过）
                    const currentFileName = fileObj.name || fileObj.originalName;
                    console.log('删除旧文件:', currentFileName);
                    await parentHandle.removeEntry(currentFileName);
                    console.log('旧文件删除成功');

                    // 更新文件句柄映射
                    this.fileHandles.set(fileObj.id, newFileHandle);
                    fileObj.fileHandle = newFileHandle;

                    console.log('文件重命名完成:', fileObj.originalName, '→', newName);
                    console.log('文件句柄已更新，ID:', fileObj.id);

                } catch (error) {
                    console.error('重命名失败:', error);
                    throw new Error(`重命名失败: ${error.message}`);
                }
            }

            async getParentDirectoryHandle(fileObj) {
                // 如果有存储的目录句柄，直接返回
                if (this.directoryHandle) {
                    console.log('使用已存储的目录句柄');
                    return this.directoryHandle;
                }

                // 如果文件是通过目录选择器添加的，尝试使用存储的目录句柄
                if (fileObj.parentDirectoryHandle) {
                    console.log('使用文件对象的父目录句柄');
                    return fileObj.parentDirectoryHandle;
                }

                // 提示用户选择包含该文件的目录
                try {
                    console.log('请求用户选择目录');
                    this.showNotification('请选择包含该文件的目录以完成重命名', 'info');

                    const directoryHandle = await window.showDirectoryPicker();
                    this.directoryHandle = directoryHandle;

                    // 验证选择的目录是否包含该文件
                    // 使用当前的文件名（可能已经被重命名过）
                    const currentFileName = fileObj.name || fileObj.originalName;
                    try {
                        await directoryHandle.getFileHandle(currentFileName);
                        console.log('目录验证成功，包含目标文件:', currentFileName);
                        return directoryHandle;
                    } catch (error) {
                        throw new Error(`选择的目录不包含文件 "${currentFileName}"`);
                    }

                } catch (error) {
                    if (error.name === 'AbortError') {
                        throw new Error('用户取消了目录选择');
                    }
                    throw error;
                }
            }

            async downloadRenamedFiles(files) {
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    const newName = file.newName || file.originalName;

                    // 创建下载链接
                    const url = URL.createObjectURL(file.file);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = newName;

                    // 触发下载
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // 释放URL对象
                    URL.revokeObjectURL(url);

                    // 添加延迟避免浏览器阻止多个下载
                    if (i < files.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                }
            }

            showNotification(message, type = 'info') {
                // 创建通知元素
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.innerHTML = `
                    <i class="fas ${type === 'success' ? 'fa-check-circle' :
                                   type === 'warning' ? 'fa-exclamation-triangle' :
                                   type === 'error' ? 'fa-times-circle' : 'fa-info-circle'}"></i>
                    <span>${message}</span>
                `;

                // 添加到页面
                document.body.appendChild(notification);

                // 显示动画
                setTimeout(() => notification.classList.add('show'), 100);

                // 自动移除
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => document.body.removeChild(notification), 300);
                }, 3000);
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            getFileIcon(type) {
                if (type.startsWith('image/')) return 'fa-image';
                if (type.startsWith('video/')) return 'fa-video';
                if (type.startsWith('audio/')) return 'fa-music';
                if (type.includes('pdf')) return 'fa-file-pdf';
                if (type.includes('word')) return 'fa-file-word';
                if (type.includes('excel')) return 'fa-file-excel';
                if (type.includes('powerpoint')) return 'fa-file-powerpoint';
                if (type.includes('zip') || type.includes('rar')) return 'fa-file-archive';
                return 'fa-file';
            }
        }

        // 重命名引擎类
        class RenameEngine {
            constructor() {
                this.setupEventListeners();
            }

            setupEventListeners() {
                // 输入框事件
                elements.prefixInput.addEventListener('input', this.updateSettings.bind(this));
                elements.suffixInput.addEventListener('input', this.updateSettings.bind(this));
                elements.findInput.addEventListener('input', this.updateSettings.bind(this));
                elements.replaceInput.addEventListener('input', this.updateSettings.bind(this));
                elements.regexMode.addEventListener('change', this.handleRegexModeChange.bind(this));

                // 重命名按钮
                elements.renameBtn.addEventListener('click', () => {
                    fileManager.batchRename();
                });

                // 自动集数功能事件
                const autoEpisodeCheck = document.getElementById('autoEpisodeCheck');
                const episodeSettings = document.getElementById('episodeSettings');
                const startEpisode = document.getElementById('startEpisode');
                const episodeDigits = document.getElementById('episodeDigits');
                const episodePrefix = document.getElementById('episodePrefix');

                if (autoEpisodeCheck) {
                    autoEpisodeCheck.addEventListener('change', (e) => {
                        if (e.target.checked) {
                            episodeSettings.classList.add('show');
                        } else {
                            episodeSettings.classList.remove('show');
                        }
                        this.updatePreview();
                        this.updatePreviewArea();
                    });
                }

                [startEpisode, episodeDigits, episodePrefix].forEach(element => {
                    if (element) {
                        element.addEventListener('input', () => {
                            this.updatePreview();
                            this.updatePreviewArea();
                        });
                    }
                });
            }

            handleRegexModeChange() {
                const isRegexMode = elements.regexMode.checked;

                if (isRegexMode) {
                    elements.regexHint.textContent = '正则表达式模式已启用';
                    elements.regexHelp.classList.add('show');
                    elements.findInput.placeholder = '正则表达式（例如：\\d{4} 或 old.*）';
                    elements.replaceInput.placeholder = '替换内容（例如：2024 或 new）';
                } else {
                    elements.regexHint.textContent = '普通文本查找替换模式';
                    elements.regexHelp.classList.remove('show');
                    elements.findInput.placeholder = '查找内容（例如：old）';
                    elements.replaceInput.placeholder = '替换为（例如：new）';
                }

                this.updateSettings();
            }

            updateSettings() {
                app.settings = {
                    prefix: elements.prefixInput.value,
                    suffix: elements.suffixInput.value,
                    findPattern: elements.findInput.value,
                    replaceWith: elements.replaceInput.value,
                    isRegexMode: elements.regexMode.checked
                };
                this.updatePreview();
                this.updatePreviewArea();
            }

            updatePreview() {
                // 检查是否启用自动集数
                const autoEpisodeEnabled = document.getElementById('autoEpisodeCheck')?.checked || false;
                const startEpisode = parseInt(document.getElementById('startEpisode')?.value || '1');
                const episodeDigits = parseInt(document.getElementById('episodeDigits')?.value || '2');
                const episodePrefix = document.getElementById('episodePrefix')?.value || 'E';

                app.files.forEach((file, index) => {
                    let newName = this.generateNewName(file.originalName);

                    // 如果启用自动集数，添加集数信息
                    if (autoEpisodeEnabled) {
                        const episodeNumber = startEpisode + index;
                        const episodeStr = episodePrefix + episodeNumber.toString().padStart(episodeDigits, '0');

                        // 在文件名中添加集数（在扩展名前）
                        const lastDotIndex = newName.lastIndexOf('.');
                        if (lastDotIndex > 0) {
                            const nameWithoutExt = newName.substring(0, lastDotIndex);
                            const extension = newName.substring(lastDotIndex);
                            newName = nameWithoutExt + '.' + episodeStr + extension;
                        } else {
                            newName = newName + '.' + episodeStr;
                        }
                    }

                    file.newName = newName;

                    const previewElement = document.getElementById(`preview-${file.id}`);
                    if (previewElement) {
                        previewElement.textContent = newName !== file.originalName ? `→ ${newName}` : '';
                    }
                });

                // 检查重复命名
                this.checkDuplicateNames();
            }

            checkDuplicateNames() {
                const nameCount = {};
                const duplicates = new Set();

                // 统计新文件名出现次数
                app.files.forEach(file => {
                    const newName = file.newName || file.originalName;
                    nameCount[newName] = (nameCount[newName] || 0) + 1;
                    if (nameCount[newName] > 1) {
                        duplicates.add(newName);
                    }
                });

                // 为重复的文件名添加序号
                const duplicateCounters = {};
                app.files.forEach(file => {
                    const newName = file.newName || file.originalName;
                    if (duplicates.has(newName)) {
                        duplicateCounters[newName] = (duplicateCounters[newName] || 0) + 1;

                        // 添加序号
                        const lastDotIndex = newName.lastIndexOf('.');
                        if (lastDotIndex > 0) {
                            const nameWithoutExt = newName.substring(0, lastDotIndex);
                            const extension = newName.substring(lastDotIndex);
                            file.newName = `${nameWithoutExt}_${duplicateCounters[newName]}${extension}`;
                        } else {
                            file.newName = `${newName}_${duplicateCounters[newName]}`;
                        }

                        // 更新预览
                        const previewElement = document.getElementById(`preview-${file.id}`);
                        if (previewElement) {
                            previewElement.textContent = `→ ${file.newName}`;
                        }
                    }
                });
            }

            generateNewName(originalName) {
                let newName = originalName;
                const { prefix, suffix, findPattern, replaceWith, isRegexMode } = app.settings;

                // 步骤1: 先执行查找替换操作
                if (findPattern && replaceWith !== undefined) {
                    try {
                        if (isRegexMode) {
                            // 正则表达式模式
                            const regex = new RegExp(findPattern, 'g');
                            newName = newName.replace(regex, replaceWith);
                        } else {
                            // 普通文本替换模式
                            newName = newName.split(findPattern).join(replaceWith);
                        }
                    } catch (e) {
                        console.warn('Invalid pattern:', findPattern, e);
                    }
                }

                // 步骤2: 分离文件名和扩展名
                const lastDotIndex = newName.lastIndexOf('.');
                let nameWithoutExt = newName;
                let extension = '';

                if (lastDotIndex > 0) {
                    nameWithoutExt = newName.substring(0, lastDotIndex);
                    extension = newName.substring(lastDotIndex);
                }

                // 步骤3: 添加前缀（如果有）
                if (prefix) {
                    nameWithoutExt = prefix + nameWithoutExt;
                }

                // 步骤4: 添加后缀（如果有）
                if (suffix) {
                    nameWithoutExt = nameWithoutExt + suffix;
                }

                return nameWithoutExt + extension;
            }

            updatePreviewArea() {
                this.updateBasicPreview();
                this.updateEpisodePreview();
                this.updateFinalPreview();
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            updateBasicPreview() {
                const basicPreviewArea = document.getElementById('basicPreviewArea');
                if (!basicPreviewArea) return;

                if (app.files.length === 0) {
                    basicPreviewArea.innerHTML = `
                        <p style="color: var(--text-secondary); text-align: center; padding: 20px;">
                            添加文件后将显示前缀、后缀、查找替换的预览效果
                        </p>
                    `;
                    return;
                }

                const { prefix, suffix, findPattern, replaceWith } = app.settings;
                const hasBasicOperations = prefix || suffix || (findPattern && replaceWith !== undefined);

                if (!hasBasicOperations) {
                    basicPreviewArea.innerHTML = `
                        <p style="color: var(--text-secondary); text-align: center; padding: 20px; font-style: italic;">
                            未设置前缀、后缀或查找替换操作
                        </p>
                    `;
                    return;
                }

                const previewItems = app.files.slice(0, 3).map(file => {
                    const basicName = this.generateNewName(file.originalName);
                    const hasChanges = basicName !== file.originalName;

                    return `
                        <div class="preview-item">
                            <div class="preview-original">${this.escapeHtml(file.originalName)}</div>
                            ${hasChanges ? `
                                <div class="preview-arrow">→</div>
                                <div class="preview-new">${this.escapeHtml(basicName)}</div>
                            ` : `
                                <div style="color: var(--text-secondary); font-style: italic;">无变化</div>
                            `}
                        </div>
                    `;
                }).join('');

                const moreFiles = app.files.length > 3 ? `
                    <div style="text-align: center; color: var(--text-secondary); font-size: 12px; margin-top: 8px;">
                        还有 ${app.files.length - 3} 个文件...
                    </div>
                ` : '';

                basicPreviewArea.innerHTML = previewItems + moreFiles;
            }

            updateEpisodePreview() {
                const episodePreviewSection = document.getElementById('episodePreviewSection');
                const episodePreviewArea = document.getElementById('episodePreviewArea');
                if (!episodePreviewSection || !episodePreviewArea) return;

                const autoEpisodeEnabled = document.getElementById('autoEpisodeCheck')?.checked;

                if (!autoEpisodeEnabled) {
                    episodePreviewSection.style.display = 'none';
                    return;
                }

                episodePreviewSection.style.display = 'block';

                if (app.files.length === 0) {
                    episodePreviewArea.innerHTML = `
                        <p style="color: var(--text-secondary); text-align: center; padding: 20px;">
                            启用自动集数递增后将显示集数编号效果
                        </p>
                    `;
                    return;
                }

                const startEpisode = parseInt(document.getElementById('startEpisode')?.value || '1');
                const episodeDigits = parseInt(document.getElementById('episodeDigits')?.value || '2');
                const episodePrefix = document.getElementById('episodePrefix')?.value || 'E';

                const previewItems = app.files.slice(0, 5).map((file, index) => {
                    const episodeNumber = startEpisode + index;
                    const episodeStr = episodePrefix + episodeNumber.toString().padStart(episodeDigits, '0');

                    // 显示集数添加效果
                    const basicName = this.generateNewName(file.originalName);
                    const lastDotIndex = basicName.lastIndexOf('.');
                    let nameWithEpisode;

                    if (lastDotIndex > 0) {
                        const nameWithoutExt = basicName.substring(0, lastDotIndex);
                        const extension = basicName.substring(lastDotIndex);
                        nameWithEpisode = nameWithoutExt + '.' + episodeStr + extension;
                    } else {
                        nameWithEpisode = basicName + '.' + episodeStr;
                    }

                    return `
                        <div class="preview-item">
                            <div class="preview-original">${this.escapeHtml(basicName)}</div>
                            <div class="preview-arrow">→</div>
                            <div class="preview-new">${this.escapeHtml(nameWithEpisode)}</div>
                            <div style="font-size: 11px; color: var(--primary-color); margin-left: 8px;">
                                +${this.escapeHtml(episodeStr)}
                            </div>
                        </div>
                    `;
                }).join('');

                const moreFiles = app.files.length > 5 ? `
                    <div style="text-align: center; color: var(--text-secondary); font-size: 12px; margin-top: 8px;">
                        还有 ${app.files.length - 5} 个文件...
                    </div>
                ` : '';

                episodePreviewArea.innerHTML = previewItems + moreFiles;
            }

            updateFinalPreview() {
                const finalPreviewArea = document.getElementById('finalPreviewArea');
                if (!finalPreviewArea) return;

                if (app.files.length === 0) {
                    finalPreviewArea.innerHTML = `
                        <p style="color: var(--text-secondary); text-align: center; padding: 20px;">
                            添加文件后将显示最终的重命名结果
                        </p>
                    `;
                    return;
                }

                const autoEpisodeEnabled = document.getElementById('autoEpisodeCheck')?.checked;
                const startEpisode = parseInt(document.getElementById('startEpisode')?.value || '1');
                const episodeDigits = parseInt(document.getElementById('episodeDigits')?.value || '2');
                const episodePrefix = document.getElementById('episodePrefix')?.value || 'E';

                const previewItems = app.files.slice(0, 5).map((file, index) => {
                    let finalName = this.generateNewName(file.originalName);

                    // 如果启用自动集数，添加集数信息
                    if (autoEpisodeEnabled) {
                        const episodeNumber = startEpisode + index;
                        const episodeStr = episodePrefix + episodeNumber.toString().padStart(episodeDigits, '0');

                        const lastDotIndex = finalName.lastIndexOf('.');
                        if (lastDotIndex > 0) {
                            const nameWithoutExt = finalName.substring(0, lastDotIndex);
                            const extension = finalName.substring(lastDotIndex);
                            finalName = nameWithoutExt + '.' + episodeStr + extension;
                        } else {
                            finalName = finalName + '.' + episodeStr;
                        }
                    }

                    const hasChanges = finalName !== file.originalName;

                    return `
                        <div class="preview-item">
                            <div class="preview-original">${this.escapeHtml(file.originalName)}</div>
                            ${hasChanges ? `
                                <div class="preview-arrow">→</div>
                                <div class="preview-new">${this.escapeHtml(finalName)}</div>
                            ` : `
                                <div style="color: var(--text-secondary); font-style: italic;">无变化</div>
                            `}
                        </div>
                    `;
                }).join('');

                const moreFiles = app.files.length > 5 ? `
                    <div style="text-align: center; color: var(--text-secondary); font-size: 12px; margin-top: 8px;">
                        还有 ${app.files.length - 5} 个文件...
                    </div>
                ` : '';

                finalPreviewArea.innerHTML = previewItems + moreFiles;
            }

            async executeRename() {
                if (app.files.length === 0) {
                    this.showNotification('请先添加文件', 'warning');
                    return;
                }

                const button = elements.renameBtn;
                const originalText = button.innerHTML;
                button.innerHTML = '<div class="loading"></div> 重命名中...';
                button.disabled = true;

                try {
                    // 模拟重命名过程（实际应用中这里会调用文件系统API）
                    await this.simulateRename();

                    // 添加到历史记录
                    app.addToHistory({
                        type: 'rename',
                        files: app.files.map(f => ({
                            id: f.id,
                            oldName: f.originalName,
                            newName: f.newName
                        })),
                        settings: { ...app.settings }
                    });

                    this.showNotification('重命名完成！', 'success');

                    // 更新原始名称为新名称
                    app.files.forEach(file => {
                        file.originalName = file.newName;
                    });

                    fileManager.renderFileList();
                    this.updatePreview();

                } catch (error) {
                    this.showNotification('重命名失败：' + error.message, 'error');
                } finally {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }

            async simulateRename() {
                // 模拟异步重命名过程
                return new Promise(resolve => {
                    setTimeout(resolve, 1000);
                });
            }

            showNotification(message, type = 'info') {
                // 创建通知元素
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 16px 20px;
                    border-radius: 8px;
                    color: white;
                    font-weight: 500;
                    z-index: 1000;
                    animation: slideIn 0.3s ease;
                    max-width: 300px;
                `;

                // 设置背景色
                const colors = {
                    success: '#10b981',
                    warning: '#f59e0b',
                    error: '#ef4444',
                    info: '#6366f1'
                };
                notification.style.background = colors[type];

                notification.textContent = message;
                document.body.appendChild(notification);

                // 自动移除
                setTimeout(() => {
                    notification.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }
        }

        // 初始化管理器
        const fileManager = new FileManager();
        const renameEngine = new RenameEngine();

        // 主题切换
        elements.themeToggle.addEventListener('click', () => {
            app.toggleTheme();
        });

        // 历史操作
        elements.undoBtn.addEventListener('click', () => app.undo());
        elements.redoBtn.addEventListener('click', () => app.redo());

        // 快捷键支持
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'z':
                        e.preventDefault();
                        if (e.shiftKey) {
                            app.redo();
                        } else {
                            app.undo();
                        }
                        break;
                    case 'Enter':
                        e.preventDefault();
                        renameEngine.executeRename();
                        break;
                }
            }
        });

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }

            .notification {
                box-shadow: var(--shadow-lg);
            }
        `;
        document.head.appendChild(style);

        // 初始化历史按钮状态
        app.updateHistoryButtons();

        // 检查浏览器兼容性
        checkBrowserCompatibility();

        // 全局函数引用，供HTML onclick使用
        window.fileManager = fileManager;
        window.renameEngine = renameEngine;

        console.log('Renamify 应用初始化完成！');
    </script>
</body>
</html>
