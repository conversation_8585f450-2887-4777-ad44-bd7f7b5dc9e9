# 直接重命名源文件功能指南

## 🎯 核心功能实现

本工具现在支持**真正的源文件重命名**，不再需要手动替换文件！

## 🔧 技术实现

### File System Access API
使用最新的 File System Access API 实现直接文件操作：
- `showOpenFilePicker()` - 选择文件并获取文件句柄
- `showDirectoryPicker()` - 选择目录并获取目录句柄
- `createWritable()` - 创建可写流
- `removeEntry()` - 删除文件

### 重命名策略
1. **方法1**：使用 `fileHandle.move()` 方法（如果支持）
2. **方法2**：创建新文件 → 复制内容 → 删除旧文件

## 📋 使用步骤

### 1. 浏览器要求
- ✅ Chrome 86+ 
- ✅ Edge 86+
- ❌ Firefox（暂不支持）
- ❌ Safari（暂不支持）

### 2. 操作流程
1. **点击"添加文件"按钮**（重要：不要拖拽）
2. 在文件选择器中选择要重命名的文件
3. 设置重命名规则（前缀、后缀、查找替换、集数递增等）
4. 在实时预览中确认效果
5. **点击"直接重命名源文件"按钮**
6. 等待重命名完成

### 3. 文件状态标识
- ✅ **"✓ 可直接重命名"** - 支持源文件修改
- ⚠️ **"⚠ 仅下载"** - 只能下载重命名副本

## 🚀 功能特性

### 批量重命名
- 一次选择多个文件
- 自动处理文件名冲突
- 显示重命名进度和结果

### 智能预览
- **基本操作预览**：前缀、后缀、查找替换效果
- **集数递增预览**：显示集数编号添加过程
- **最终结果预览**：所有操作的综合效果

### 错误处理
- 权限不足时的友好提示
- 文件被占用时的处理
- 重命名失败时的回滚机制

## ⚠️ 注意事项

### 权限要求
- 首次使用需要授权文件系统访问权限
- 某些系统文件可能无法重命名
- 文件被其他程序占用时无法重命名

### 安全考虑
- 所有操作在本地进行，不上传到服务器
- 支持操作撤销（如果可能）
- 自动备份重要文件信息

### 兼容性回退
- 不支持的浏览器自动隐藏直接重命名按钮
- 提供下载重命名文件的备选方案
- 清晰的功能支持状态提示

## 🔍 故障排除

### 常见问题

**Q: 为什么看不到"直接重命名源文件"按钮？**
A: 您的浏览器不支持 File System Access API，请使用 Chrome 86+ 或 Edge 86+。

**Q: 点击按钮没有反应？**
A: 请确保：
1. 已通过"添加文件"按钮选择文件（不是拖拽）
2. 文件列表中显示"✓ 可直接重命名"
3. 已设置重命名规则且有实际变化

**Q: 重命名失败怎么办？**
A: 可能原因：
1. 文件被其他程序占用
2. 没有足够的文件系统权限
3. 目标文件名已存在

**Q: 如何处理文件名冲突？**
A: 工具会自动在重复的文件名后添加序号，如：
- `文件.txt` → `文件_1.txt`
- `文件.txt` → `文件_2.txt`

## 🎉 成功案例

### 电视剧重命名
```
原文件：episode1.mp4, episode2.mp4, episode3.mp4
设置：前缀"[2024]", 集数递增E01
结果：[2024]episode1.E01.mp4, [2024]episode2.E02.mp4, [2024]episode3.E03.mp4
```

### 批量添加日期
```
原文件：document.pdf, image.jpg, video.mp4
设置：前缀"2024-01-01_"
结果：2024-01-01_document.pdf, 2024-01-01_image.jpg, 2024-01-01_video.mp4
```

### 正则表达式替换
```
原文件：file2023.txt, doc2023.pdf
设置：查找"\d{4}", 替换"2024", 正则模式
结果：file2024.txt, doc2024.pdf
```

## 🔮 未来计划

- 支持更多浏览器
- 添加文件重命名历史记录
- 支持自定义重命名模板
- 集成云存储服务
- 添加文件内容预览

---

**享受真正的文件重命名体验！不再需要手动替换文件！** 🎉
